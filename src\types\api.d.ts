/**
 * @fileoverview API TypeScript tip tanımları
 * @description Bu dosya API request/response tipleri ve HTTP işlemleri için
 * tip tanımlarını içerir. SOLID prensiplerinden Single Responsibility Principle'a
 * uygun olarak sadece API ile ilgili tipler tanımlanır.
 */

import type { 
  ID, 
  Result, 
  PaginationParams, 
  PaginatedResult, 
  SortParams, 
  FilterCondition 
} from './global';

/**
 * @description HTTP Types - HTTP işlemleri için temel tipler
 * Interface Segregation Principle'a uygun olarak
 * küçük, özel amaçlı arayüzler tanımlanır.
 */

/**
 * @type {HttpMethod}
 * @description HTTP metodları
 */
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

/**
 * @type {HttpStatusCode}
 * @description Yaygın HTTP durum kodları
 */
export type HttpStatusCode = 
  | 200  // OK
  | 201  // Created
  | 204  // No Content
  | 400  // Bad Request
  | 401  // Unauthorized
  | 403  // Forbidden
  | 404  // Not Found
  | 409  // Conflict
  | 422  // Unprocessable Entity
  | 500  // Internal Server Error
  | 502  // Bad Gateway
  | 503; // Service Unavailable

/**
 * @interface HttpHeaders
 * @description HTTP başlıkları için arayüz
 */
export interface HttpHeaders {
  readonly [key: string]: string | undefined;
  readonly 'Content-Type'?: string;
  readonly 'Authorization'?: string;
  readonly 'Accept'?: string;
  readonly 'Accept-Language'?: string;
}

/**
 * @description Request Types - İstek tipleri
 * Dependency Inversion Principle'a uygun olarak
 * soyut arayüzler tanımlanır.
 */

/**
 * @interface BaseRequest
 * @description Tüm API isteklerinin temel arayüzü
 */
export interface BaseRequest {
  readonly headers?: HttpHeaders;
  readonly timeout?: number;
}

/**
 * @interface GetRequest
 * @description GET istekleri için arayüz
 */
export interface GetRequest extends BaseRequest {
  readonly params?: Record<string, string | number | boolean>;
}

/**
 * @interface PostRequest<T>
 * @description POST istekleri için arayüz
 * @template T - Gönderilecek veri tipi
 */
export interface PostRequest<T = unknown> extends BaseRequest {
  readonly data: T;
}

/**
 * @interface PutRequest<T>
 * @description PUT istekleri için arayüz
 * @template T - Güncellenecek veri tipi
 */
export interface PutRequest<T = unknown> extends BaseRequest {
  readonly data: T;
}

/**
 * @interface PatchRequest<T>
 * @description PATCH istekleri için arayüz
 * @template T - Kısmi güncelleme veri tipi
 */
export interface PatchRequest<T = unknown> extends BaseRequest {
  readonly data: Partial<T>;
}

/**
 * @interface DeleteRequest
 * @description DELETE istekleri için arayüz
 */
export interface DeleteRequest extends BaseRequest {
  readonly force?: boolean;
}

/**
 * @description Response Types - Yanıt tipleri
 */

/**
 * @interface ApiResponse<T>
 * @description API yanıtlarının temel arayüzü
 * @template T - Yanıt verisi tipi
 */
export interface ApiResponse<T = unknown> {
  readonly data: T;
  readonly status: HttpStatusCode;
  readonly statusText: string;
  readonly headers: HttpHeaders;
}

/**
 * @interface ApiSuccessResponse<T>
 * @description Başarılı API yanıtları için arayüz
 * @template T - Yanıt verisi tipi
 */
export interface ApiSuccessResponse<T = unknown> extends ApiResponse<T> {
  readonly success: true;
  readonly message?: string;
}

/**
 * @interface ApiErrorResponse
 * @description Hatalı API yanıtları için arayüz
 */
export interface ApiErrorResponse extends ApiResponse<null> {
  readonly success: false;
  readonly error: ApiError;
}

/**
 * @interface ApiError
 * @description API hata detayları
 */
export interface ApiError {
  readonly code: string;
  readonly message: string;
  readonly details?: Record<string, unknown>;
  readonly timestamp: string;
  readonly path?: string;
}

/**
 * @description Query Types - Sorgu tipleri
 * Open/Closed Principle'a uygun olarak genişlemeye açık yapılar
 */

/**
 * @interface QueryParams<T>
 * @description Genel sorgu parametreleri
 * @template T - Sorgulanacak veri tipi
 */
export interface QueryParams<T = unknown> {
  readonly pagination?: PaginationParams;
  readonly sort?: SortParams<T>[];
  readonly filters?: FilterCondition<T>[];
  readonly search?: string;
  readonly include?: string[];
  readonly exclude?: string[];
}

/**
 * @interface ListQueryParams<T>
 * @description Liste sorguları için parametreler
 * @template T - Liste elemanı tipi
 */
export interface ListQueryParams<T = unknown> extends QueryParams<T> {
  readonly fields?: (keyof T)[];
}

/**
 * @interface SearchQueryParams
 * @description Arama sorguları için parametreler
 */
export interface SearchQueryParams {
  readonly query: string;
  readonly fields?: string[];
  readonly fuzzy?: boolean;
  readonly highlight?: boolean;
}

/**
 * @description CRUD Operation Types - CRUD işlem tipleri
 */

/**
 * @interface CreateOperation<T>
 * @description Oluşturma işlemi arayüzü
 * @template T - Oluşturulacak veri tipi
 */
export interface CreateOperation<T> {
  readonly data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>;
}

/**
 * @interface ReadOperation
 * @description Okuma işlemi arayüzü
 */
export interface ReadOperation {
  readonly id: ID;
  readonly include?: string[];
}

/**
 * @interface UpdateOperation<T>
 * @description Güncelleme işlemi arayüzü
 * @template T - Güncellenecek veri tipi
 */
export interface UpdateOperation<T> {
  readonly id: ID;
  readonly data: Partial<Omit<T, 'id' | 'createdAt' | 'updatedAt'>>;
}

/**
 * @interface DeleteOperation
 * @description Silme işlemi arayüzü
 */
export interface DeleteOperation {
  readonly id: ID;
  readonly force?: boolean;
}

/**
 * @description Batch Operation Types - Toplu işlem tipleri
 */

/**
 * @interface BatchCreateOperation<T>
 * @description Toplu oluşturma işlemi
 * @template T - Oluşturulacak veri tipi
 */
export interface BatchCreateOperation<T> {
  readonly items: CreateOperation<T>['data'][];
  readonly validateAll?: boolean;
}

/**
 * @interface BatchUpdateOperation<T>
 * @description Toplu güncelleme işlemi
 * @template T - Güncellenecek veri tipi
 */
export interface BatchUpdateOperation<T> {
  readonly updates: UpdateOperation<T>[];
  readonly validateAll?: boolean;
}

/**
 * @interface BatchDeleteOperation
 * @description Toplu silme işlemi
 */
export interface BatchDeleteOperation {
  readonly ids: ID[];
  readonly force?: boolean;
}

/**
 * @description API Result Types - API sonuç tipleri
 * Result pattern kullanarak hata yönetimi
 */

/**
 * @type {ApiResult<T>}
 * @description API işlem sonucu tipi
 * @template T - Başarı durumunda dönen veri tipi
 */
export type ApiResult<T> = Result<ApiSuccessResponse<T>, ApiErrorResponse>;

/**
 * @type {ListResult<T>}
 * @description Liste API sonucu tipi
 * @template T - Liste elemanı tipi
 */
export type ListResult<T> = ApiResult<PaginatedResult<T>>;

/**
 * @type {CreateResult<T>}
 * @description Oluşturma API sonucu tipi
 * @template T - Oluşturulan veri tipi
 */
export type CreateResult<T> = ApiResult<T>;

/**
 * @type {UpdateResult<T>}
 * @description Güncelleme API sonucu tipi
 * @template T - Güncellenen veri tipi
 */
export type UpdateResult<T> = ApiResult<T>;

/**
 * @type {DeleteResult}
 * @description Silme API sonucu tipi
 */
export type DeleteResult = ApiResult<{ deleted: boolean; id: ID }>;

/**
 * @description Authentication Types - Kimlik doğrulama tipleri
 */

/**
 * @interface AuthTokens
 * @description Kimlik doğrulama token'ları
 */
export interface AuthTokens {
  readonly accessToken: string;
  readonly refreshToken: string;
  readonly expiresIn: number;
  readonly tokenType: 'Bearer';
}

/**
 * @interface LoginRequest
 * @description Giriş isteği
 */
export interface LoginRequest {
  readonly email: string;
  readonly password: string;
  readonly rememberMe?: boolean;
}

/**
 * @interface RefreshTokenRequest
 * @description Token yenileme isteği
 */
export interface RefreshTokenRequest {
  readonly refreshToken: string;
}
