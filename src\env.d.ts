declare namespace NodeJS {
  interface ProcessEnv {
    NODE_ENV: string;
    VUE_ROUTER_MODE: 'hash' | 'history' | 'abstract' | undefined;
    VUE_ROUTER_BASE: string | undefined;

    // Logging Configuration
    LOG_LEVEL: 'debug' | 'info' | 'warn' | 'error' | undefined;
    LOG_TO_CONSOLE: 'true' | 'false' | undefined;
    LOG_TO_FILE: 'true' | 'false' | undefined;

    // API Configuration
    API_BASE_URL: string | undefined;
    API_TIMEOUT: string | undefined;

    // Firebase Configuration (optional)
    FIREBASE_API_KEY: string | undefined;
    FIREBASE_AUTH_DOMAIN: string | undefined;
    FIREBASE_PROJECT_ID: string | undefined;

    // Application Configuration
    APP_NAME: string | undefined;
    APP_VERSION: string | undefined;
    APP_DESCRIPTION: string | undefined;
  }
}

/**
 * @description Vue 3 Global Properties
 * Vue 3 uygulamasının global özelliklerini genişletir
 */
import 'vue';
import type { QNotifyOptions, QLoadingOptions, QDialogProps } from './types';

declare module 'vue' {
  interface ComponentCustomProperties {
    readonly $t: (key: string, params?: Record<string, unknown>) => string;
    readonly $notify: (options: QNotifyOptions) => void;
    readonly $loading: {
      readonly show: (options?: QLoadingOptions) => void;
      readonly hide: () => void;
    };
    readonly $dialog: {
      readonly show: (options: QDialogProps) => Promise<unknown>;
      readonly hide: () => void;
    };
  }
}

/**
 * @description Vue Router Module Augmentation
 * Vue Router'ın RouteMeta arayüzünü genişleterek özel meta veri özelliklerini ekler.
 * Bu, rotalara dinamik navigasyon menüsü oluşturma gibi ek bilgiler eklememizi sağlar.
 */
import 'vue-router';

declare module 'vue-router' {
  interface RouteMeta {
    /**
     * @property {string} title
     * @description Navigasyon menüsünde veya sayfa başlığında görüntülenecek başlık.
     * i18n anahtarı olarak kullanılır.
     */
    title?: string;

    /**
     * @property {boolean} showInNav
     * @description Bu rotanın navigasyon menüsünde gösterilip gösterilmeyeceği
     */
    showInNav?: boolean;

    /**
     * @property {string} icon
     * @description Quasar ikon adı (örneğin: 'home', 'settings')
     */
    icon?: string;

    /**
     * @property {string[]} permissions
     * @description Bu rotaya erişim için gerekli izinler
     */
    permissions?: readonly string[];

    /**
     * @property {boolean} requiresAuth
     * @description Bu rotanın kimlik doğrulama gerektirip gerektirmediği
     */
    requiresAuth?: boolean;

    /**
     * @property {string} layout
     * @description Bu rota için kullanılacak layout
     */
    layout?: string;

    /**
     * @property {Record<string, unknown>} breadcrumb
     * @description Breadcrumb navigasyonu için bilgiler
     */
    breadcrumb?: {
      readonly label: string;
      readonly to?: string;
      readonly icon?: string;
    };

    /**
     * @property {unknown} meta
     * @description Diğer özel meta veriler için genişletilebilir alan
     */
    [key: string]: unknown;
  }
}

/**
 * @description Pinia Store Module Augmentation
 * Pinia store'larına özel özellikler ekler
 */
import 'pinia';

declare module 'pinia' {
  export interface DefineStoreOptionsBase<S, Store> {
    /**
     * @property {boolean} persist
     * @description Store'un localStorage'da kalıcı olup olmayacağı
     */
    persist?: boolean;

    /**
     * @property {string} persistKey
     * @description Kalıcılık için kullanılacak anahtar
     */
    persistKey?: string;
  }
}

/**
 * @description Quasar Global Properties
 * Quasar'ın global özelliklerini genişletir
 */
import 'quasar';

declare module 'quasar' {
  interface QuasarPluginOptions {
    /**
     * @property {Record<string, unknown>} config
     * @description Özel Quasar konfigürasyonu
     */
    config?: Record<string, unknown>;

    /**
     * @property {string[]} plugins
     * @description Yüklenecek Quasar eklentileri
     */
    plugins?: readonly string[];
  }
}
