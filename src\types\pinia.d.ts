/**
 * @fileoverview Pinia Store TypeScript tip tanımları
 * @description Bu dosya Pinia store'ları için tip tanımlarını içerir.
 * SOLID prensiplerinden Single Responsibility Principle'a uygun olarak
 * sadece Pinia store ile ilgili tipler tanımlanır.
 */

import type { Ref, ComputedRef } from 'vue';
import type { ID, Result } from './global';

/**
 * @description Base Store Types - Temel store tipleri
 * Interface Segregation Principle'a uygun olarak
 * küçük, özel amaçlı arayüzler tanımlanır.
 */

/**
 * @interface BaseStoreState
 * @description Tüm store'ların sahip olması gereken temel state
 */
export interface BaseStoreState {
  readonly loading: boolean;
  readonly error: string | null;
  readonly initialized: boolean;
}

/**
 * @interface BaseStoreActions
 * @description Tüm store'ların sahip olması gereken temel actions
 */
export interface BaseStoreActions {
  readonly setLoading: (loading: boolean) => void;
  readonly setError: (error: string | null) => void;
  readonly clearError: () => void;
  readonly reset: () => void;
}

/**
 * @interface BaseStoreGetters
 * @description Tüm store'ların sahip olması gereken temel getters
 */
export interface BaseStoreGetters {
  readonly isLoading: ComputedRef<boolean>;
  readonly hasError: ComputedRef<boolean>;
  readonly isInitialized: ComputedRef<boolean>;
}

/**
 * @description Theme Store Types - Tema store tipleri
 */

/**
 * @type {ThemeMode}
 * @description Tema modları
 */
export type ThemeMode = 'light' | 'dark' | 'auto';

/**
 * @interface ThemeStoreState
 * @description Tema store state arayüzü
 */
export interface ThemeStoreState extends BaseStoreState {
  readonly mode: ThemeMode;
  readonly systemPreference: 'light' | 'dark';
  readonly customColors: Record<string, string>;
}

/**
 * @interface ThemeStoreActions
 * @description Tema store actions arayüzü
 */
export interface ThemeStoreActions extends BaseStoreActions {
  readonly setMode: (mode: ThemeMode) => void;
  readonly toggleMode: () => void;
  readonly setCustomColor: (key: string, value: string) => void;
  readonly removeCustomColor: (key: string) => void;
  readonly detectSystemPreference: () => void;
  readonly applyTheme: () => void;
}

/**
 * @interface ThemeStoreGetters
 * @description Tema store getters arayüzü
 */
export interface ThemeStoreGetters extends BaseStoreGetters {
  readonly isDark: ComputedRef<boolean>;
  readonly isLight: ComputedRef<boolean>;
  readonly isAuto: ComputedRef<boolean>;
  readonly currentTheme: ComputedRef<'light' | 'dark'>;
  readonly themeColors: ComputedRef<Record<string, string>>;
}

/**
 * @interface ThemeStore
 * @description Tema store tam arayüzü
 */
export interface ThemeStore extends ThemeStoreState, ThemeStoreActions, ThemeStoreGetters {}

/**
 * @description Language Store Types - Dil store tipleri
 */

/**
 * @type {SupportedLocale}
 * @description Desteklenen diller
 */
export type SupportedLocale = 'tr-TR' | 'en-US';

/**
 * @interface LanguageStoreState
 * @description Dil store state arayüzü
 */
export interface LanguageStoreState extends BaseStoreState {
  readonly currentLocale: SupportedLocale;
  readonly availableLocales: readonly SupportedLocale[];
  readonly fallbackLocale: SupportedLocale;
  readonly translations: Record<SupportedLocale, Record<string, string>>;
}

/**
 * @interface LanguageStoreActions
 * @description Dil store actions arayüzü
 */
export interface LanguageStoreActions extends BaseStoreActions {
  readonly setLocale: (locale: SupportedLocale) => Promise<void>;
  readonly loadTranslations: (locale: SupportedLocale) => Promise<void>;
  readonly addTranslation: (locale: SupportedLocale, key: string, value: string) => void;
  readonly removeTranslation: (locale: SupportedLocale, key: string) => void;
  readonly detectBrowserLocale: () => SupportedLocale;
}

/**
 * @interface LanguageStoreGetters
 * @description Dil store getters arayüzü
 */
export interface LanguageStoreGetters extends BaseStoreGetters {
  readonly isRTL: ComputedRef<boolean>;
  readonly currentTranslations: ComputedRef<Record<string, string>>;
  readonly localeDisplayName: ComputedRef<string>;
  readonly t: ComputedRef<(key: string, params?: Record<string, unknown>) => string>;
}

/**
 * @interface LanguageStore
 * @description Dil store tam arayüzü
 */
export interface LanguageStore extends LanguageStoreState, LanguageStoreActions, LanguageStoreGetters {}

/**
 * @description Layout Store Types - Layout store tipleri
 */

/**
 * @type {LayoutMode}
 * @description Layout modları
 */
export type LayoutMode = 'desktop' | 'mobile' | 'tablet';

/**
 * @interface LayoutStoreState
 * @description Layout store state arayüzü
 */
export interface LayoutStoreState extends BaseStoreState {
  readonly drawerOpen: boolean;
  readonly drawerMini: boolean;
  readonly headerHeight: number;
  readonly footerHeight: number;
  readonly screenWidth: number;
  readonly screenHeight: number;
  readonly layoutMode: LayoutMode;
  readonly breakpoints: Record<string, number>;
}

/**
 * @interface LayoutStoreActions
 * @description Layout store actions arayüzü
 */
export interface LayoutStoreActions extends BaseStoreActions {
  readonly toggleDrawer: () => void;
  readonly openDrawer: () => void;
  readonly closeDrawer: () => void;
  readonly setDrawerMini: (mini: boolean) => void;
  readonly toggleDrawerMini: () => void;
  readonly setHeaderHeight: (height: number) => void;
  readonly setFooterHeight: (height: number) => void;
  readonly updateScreenSize: (width: number, height: number) => void;
  readonly setBreakpoint: (name: string, value: number) => void;
}

/**
 * @interface LayoutStoreGetters
 * @description Layout store getters arayüzü
 */
export interface LayoutStoreGetters extends BaseStoreGetters {
  readonly isDesktop: ComputedRef<boolean>;
  readonly isMobile: ComputedRef<boolean>;
  readonly isTablet: ComputedRef<boolean>;
  readonly contentHeight: ComputedRef<number>;
  readonly drawerWidth: ComputedRef<number>;
  readonly isDrawerVisible: ComputedRef<boolean>;
  readonly currentBreakpoint: ComputedRef<string>;
}

/**
 * @interface LayoutStore
 * @description Layout store tam arayüzü
 */
export interface LayoutStore extends LayoutStoreState, LayoutStoreActions, LayoutStoreGetters {}

/**
 * @description User Store Types - Kullanıcı store tipleri
 */

/**
 * @interface User
 * @description Kullanıcı veri modeli
 */
export interface User {
  readonly id: ID;
  readonly email: string;
  readonly name: string;
  readonly avatar?: string;
  readonly role: string;
  readonly permissions: readonly string[];
  readonly preferences: Record<string, unknown>;
  readonly lastLoginAt?: string;
  readonly createdAt: string;
  readonly updatedAt: string;
}

/**
 * @interface UserStoreState
 * @description Kullanıcı store state arayüzü
 */
export interface UserStoreState extends BaseStoreState {
  readonly currentUser: User | null;
  readonly isAuthenticated: boolean;
  readonly accessToken: string | null;
  readonly refreshToken: string | null;
  readonly tokenExpiresAt: number | null;
  readonly loginAttempts: number;
  readonly lastLoginError: string | null;
}

/**
 * @interface LoginCredentials
 * @description Giriş bilgileri
 */
export interface LoginCredentials {
  readonly email: string;
  readonly password: string;
  readonly rememberMe?: boolean;
}

/**
 * @interface UserStoreActions
 * @description Kullanıcı store actions arayüzü
 */
export interface UserStoreActions extends BaseStoreActions {
  readonly login: (credentials: LoginCredentials) => Promise<Result<User, string>>;
  readonly logout: () => Promise<void>;
  readonly refreshAccessToken: () => Promise<Result<string, string>>;
  readonly updateProfile: (data: Partial<User>) => Promise<Result<User, string>>;
  readonly changePassword: (oldPassword: string, newPassword: string) => Promise<Result<void, string>>;
  readonly updatePreferences: (preferences: Record<string, unknown>) => Promise<Result<void, string>>;
  readonly checkAuthStatus: () => Promise<boolean>;
  readonly clearAuthData: () => void;
}

/**
 * @interface UserStoreGetters
 * @description Kullanıcı store getters arayüzü
 */
export interface UserStoreGetters extends BaseStoreGetters {
  readonly userName: ComputedRef<string>;
  readonly userEmail: ComputedRef<string>;
  readonly userAvatar: ComputedRef<string | null>;
  readonly userRole: ComputedRef<string>;
  readonly userPermissions: ComputedRef<readonly string[]>;
  readonly hasPermission: ComputedRef<(permission: string) => boolean>;
  readonly isTokenExpired: ComputedRef<boolean>;
  readonly shouldRefreshToken: ComputedRef<boolean>;
  readonly canLogin: ComputedRef<boolean>;
}

/**
 * @interface UserStore
 * @description Kullanıcı store tam arayüzü
 */
export interface UserStore extends UserStoreState, UserStoreActions, UserStoreGetters {}

/**
 * @description Store Factory Types - Store factory tipleri
 * Dependency Inversion Principle'a uygun olarak soyut factory pattern
 */

/**
 * @interface StoreFactory<T>
 * @description Store factory arayüzü
 * @template T - Store tipi
 */
export interface StoreFactory<T> {
  readonly create: () => T;
  readonly destroy: (store: T) => void;
  readonly reset: (store: T) => void;
}

/**
 * @interface StoreRegistry
 * @description Store kayıt defteri arayüzü
 */
export interface StoreRegistry {
  readonly register: <T>(name: string, factory: StoreFactory<T>) => void;
  readonly unregister: (name: string) => void;
  readonly get: <T>(name: string) => T | null;
  readonly has: (name: string) => boolean;
  readonly clear: () => void;
}

/**
 * @description Store Persistence Types - Store kalıcılık tipleri
 */

/**
 * @interface StorePersistenceOptions
 * @description Store kalıcılık seçenekleri
 */
export interface StorePersistenceOptions {
  readonly key: string;
  readonly storage: 'localStorage' | 'sessionStorage' | 'indexedDB';
  readonly paths?: readonly string[];
  readonly beforeRestore?: (context: unknown) => void;
  readonly afterRestore?: (context: unknown) => void;
  readonly serializer?: {
    readonly serialize: (value: unknown) => string;
    readonly deserialize: (value: string) => unknown;
  };
}

/**
 * @interface PersistentStore<T>
 * @description Kalıcı store arayüzü
 * @template T - Store state tipi
 */
export interface PersistentStore<T> {
  readonly $persist: {
    readonly save: () => Promise<void>;
    readonly restore: () => Promise<void>;
    readonly clear: () => Promise<void>;
    readonly isHydrated: Ref<boolean>;
  };
}
