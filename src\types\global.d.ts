/**
 * @fileoverview Global TypeScript tip tanımları
 * @description Bu dosya projenin her yerinde kullanılabilecek genel tip tanımlarını içerir.
 * SOLID prensiplerinden Single Responsibility Principle'a uygun olarak
 * sadece global utility tipleri ve ortak veri yapıları tanımlanır.
 */

/**
 * @description Utility Types - Yeniden kullanılabilir tip yardımcıları
 * Bu tipler Open/Closed Principle'a uygun olarak genişlemeye açık,
 * değişime kapalı şekilde tasarlanmıştır.
 */

/**
 * @type {DeepPartial<T>}
 * @description Bir nesnenin tüm özelliklerini (iç içe) opsiyonel yapar
 * @template T - Partial yapılacak tip
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

/**
 * @type {DeepRequired<T>}
 * @description Bir nesnenin tüm özelliklerini (iç içe) zorunlu yapar
 * @template T - Required yapılacak tip
 */
export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P];
};

/**
 * @type {Optional<T, K>}
 * @description Belirtilen anahtarları opsiyonel yapar
 * @template T - Ana tip
 * @template K - Opsiyonel yapılacak anahtarlar
 */
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * @type {RequiredKeys<T>}
 * @description Bir tipte zorunlu olan anahtarları döndürür
 * @template T - İncelenecek tip
 */
export type RequiredKeys<T> = {
  // eslint-disable-next-line @typescript-eslint/no-empty-object-type
  [K in keyof T]-?: {} extends Pick<T, K> ? never : K;
}[keyof T];

/**
 * @type {OptionalKeys<T>}
 * @description Bir tipte opsiyonel olan anahtarları döndürür
 * @template T - İncelenecek tip
 */
export type OptionalKeys<T> = {
  // eslint-disable-next-line @typescript-eslint/no-empty-object-type
  [K in keyof T]-?: {} extends Pick<T, K> ? K : never;
}[keyof T];

/**
 * @description Brand Types - Tip güvenliği için marka tipleri
 * Bu tipler runtime'da aynı olsa da compile-time'da farklı tiplerdir.
 * Dependency Inversion Principle'a uygun olarak soyut tiplerdir.
 */

/**
 * @type {Brand<T, B>}
 * @description Bir tipe marka ekleyerek tip güvenliği sağlar
 * @template T - Ana tip
 * @template B - Marka adı
 */
export type Brand<T, B> = T & { readonly __brand: B };

/**
 * @type {ID}
 * @description Benzersiz kimlik tipi - string tabanlı ama tip güvenli
 */
export type ID = Brand<string, 'ID'>;

/**
 * @type {Email}
 * @description Email adresi tipi - string tabanlı ama tip güvenli
 */
export type Email = Brand<string, 'Email'>;

/**
 * @type {URL}
 * @description URL tipi - string tabanlı ama tip güvenli
 */
export type URL = Brand<string, 'URL'>;

/**
 * @type {Timestamp}
 * @description Zaman damgası tipi - number tabanlı ama tip güvenli
 */
export type Timestamp = Brand<number, 'Timestamp'>;

/**
 * @description Common Data Structures - Ortak veri yapıları
 * Interface Segregation Principle'a uygun olarak
 * küçük, özel amaçlı arayüzler tanımlanır.
 */

/**
 * @interface BaseEntity
 * @description Tüm varlıkların sahip olması gereken temel özellikler
 * Single Responsibility: Sadece temel varlık özelliklerini tanımlar
 */
export interface BaseEntity {
  readonly id: ID;
  readonly createdAt: Timestamp;
  readonly updatedAt: Timestamp;
}

/**
 * @interface Identifiable
 * @description Kimlik sahibi varlıklar için arayüz
 */
export interface Identifiable {
  readonly id: ID;
}

/**
 * @interface Timestamped
 * @description Zaman damgası sahibi varlıklar için arayüz
 */
export interface Timestamped {
  readonly createdAt: Timestamp;
  readonly updatedAt: Timestamp;
}

/**
 * @interface Nameable
 * @description İsim sahibi varlıklar için arayüz
 */
export interface Nameable {
  readonly name: string;
}

/**
 * @interface Describable
 * @description Açıklama sahibi varlıklar için arayüz
 */
export interface Describable {
  readonly description?: string;
}

/**
 * @description Result Types - Hata yönetimi için sonuç tipleri
 * Functional programming yaklaşımıyla hata yönetimi
 */

/**
 * @type {Result<T, E>}
 * @description Başarı veya hata sonucunu temsil eden tip
 * @template T - Başarı durumunda dönen tip
 * @template E - Hata durumunda dönen tip
 */
export type Result<T, E = Error> = Success<T> | Failure<E>;

/**
 * @interface Success<T>
 * @description Başarılı sonuç arayüzü
 */
export interface Success<T> {
  readonly success: true;
  readonly data: T;
}

/**
 * @interface Failure<E>
 * @description Başarısız sonuç arayüzü
 */
export interface Failure<E> {
  readonly success: false;
  readonly error: E;
}

/**
 * @description Pagination Types - Sayfalama tipleri
 */

/**
 * @interface PaginationParams
 * @description Sayfalama parametreleri
 */
export interface PaginationParams {
  readonly page: number;
  readonly limit: number;
  readonly offset?: number;
}

/**
 * @interface PaginatedResult<T>
 * @description Sayfalanmış sonuç arayüzü
 * @template T - Sayfalanan veri tipi
 */
export interface PaginatedResult<T> {
  readonly data: readonly T[];
  readonly total: number;
  readonly page: number;
  readonly limit: number;
  readonly hasNext: boolean;
  readonly hasPrevious: boolean;
}

/**
 * @description Sort Types - Sıralama tipleri
 */

/**
 * @type {SortDirection}
 * @description Sıralama yönü
 */
export type SortDirection = 'asc' | 'desc';

/**
 * @interface SortParams<T>
 * @description Sıralama parametreleri
 * @template T - Sıralanacak nesne tipi
 */
export interface SortParams<T> {
  readonly field: keyof T;
  readonly direction: SortDirection;
}

/**
 * @description Filter Types - Filtreleme tipleri
 */

/**
 * @type {FilterOperator}
 * @description Filtreleme operatörleri
 */
export type FilterOperator =
  | 'eq'      // equals
  | 'ne'      // not equals
  | 'gt'      // greater than
  | 'gte'     // greater than or equal
  | 'lt'      // less than
  | 'lte'     // less than or equal
  | 'in'      // in array
  | 'nin'     // not in array
  | 'like'    // contains
  | 'ilike';  // case insensitive contains

/**
 * @interface FilterCondition<T>
 * @description Filtreleme koşulu
 * @template T - Filtrelenecek nesne tipi
 */
export interface FilterCondition<T> {
  readonly field: keyof T;
  readonly operator: FilterOperator;
  readonly value: unknown;
}

/**
 * @description Validation Types - Doğrulama tipleri
 */

/**
 * @interface ValidationError
 * @description Doğrulama hatası arayüzü
 */
export interface ValidationError {
  readonly field: string;
  readonly message: string;
  readonly code: string;
}

/**
 * @type {ValidationResult<T>}
 * @description Doğrulama sonucu tipi
 * @template T - Doğrulanan veri tipi
 */
export type ValidationResult<T> = Result<T, ValidationError[]>;
