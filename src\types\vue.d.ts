/**
 * @fileoverview Vue 3 TypeScript tip tanımları
 * @description Bu dosya Vue 3 Composition API, component props, emits ve
 * composable'lar için tip tanımlarını içerir. SOLID prensiplerinden
 * Single Responsibility Principle'a uygun olarak sadece Vue ile ilgili tipler tanımlanır.
 */

import type { Ref, ComputedRef, WritableComputedRef } from 'vue';
import type { RouteLocationNormalized, Router } from 'vue-router';
import type { Result, ValidationResult } from './global';

/**
 * @description Vue Component Types - Vue bileşen tipleri
 * Interface Segregation Principle'a uygun olarak
 * küçük, özel amaçlı arayüzler tanımlanır.
 */

/**
 * @interface BaseComponentProps
 * @description Tüm bileşenlerin sahip olabileceği temel props
 */
export interface BaseComponentProps {
  readonly id?: string;
  readonly class?: string | string[] | Record<string, boolean>;
  readonly style?: string | Record<string, string>;
  readonly testId?: string;
}

/**
 * @interface LoadingProps
 * @description Yükleme durumu props'ları
 */
export interface LoadingProps {
  readonly loading?: boolean;
  readonly loadingText?: string;
  readonly loadingColor?: string;
}

/**
 * @interface DisabledProps
 * @description Devre dışı bırakma props'ları
 */
export interface DisabledProps {
  readonly disabled?: boolean;
  readonly readonly?: boolean;
}

/**
 * @interface SizeProps
 * @description Boyut props'ları
 */
export interface SizeProps {
  readonly size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}

/**
 * @interface ColorProps
 * @description Renk props'ları
 */
export interface ColorProps {
  readonly color?: string;
  readonly textColor?: string;
  readonly bgColor?: string;
}

/**
 * @description Form Types - Form bileşen tipleri
 */

/**
 * @interface FormFieldProps<T>
 * @description Form alanları için temel props
 * @template T - Alan değer tipi
 */
export interface FormFieldProps<T = unknown> extends BaseComponentProps, DisabledProps {
  readonly modelValue?: T;
  readonly label?: string;
  readonly placeholder?: string;
  readonly hint?: string;
  readonly error?: boolean;
  readonly errorMessage?: string;
  readonly required?: boolean;
  readonly clearable?: boolean;
}

/**
 * @interface FormFieldEmits<T>
 * @description Form alanları için temel emits
 * @template T - Alan değer tipi
 */
export interface FormFieldEmits<T = unknown> {
  'update:modelValue': [value: T];
  'focus': [event: FocusEvent];
  'blur': [event: FocusEvent];
  'clear': [];
}

/**
 * @interface ValidationProps
 * @description Doğrulama props'ları
 */
export interface ValidationProps {
  readonly rules?: ValidationRule[];
  readonly validateOnBlur?: boolean;
  readonly validateOnChange?: boolean;
  readonly lazyValidation?: boolean;
}

/**
 * @type {ValidationRule}
 * @description Doğrulama kuralı tipi
 */
export type ValidationRule = (value: unknown) => boolean | string;

/**
 * @description Table Types - Tablo bileşen tipleri
 */

/**
 * @interface TableColumn<T>
 * @description Tablo sütunu tanımı
 * @template T - Satır veri tipi
 */
export interface TableColumn<T = Record<string, unknown>> {
  readonly name: string;
  readonly label: string;
  readonly field: keyof T | ((row: T) => unknown);
  readonly align?: 'left' | 'center' | 'right';
  readonly sortable?: boolean;
  readonly sort?: (a: unknown, b: unknown) => number;
  readonly format?: (value: unknown, row: T) => string;
  readonly style?: string | ((row: T) => string);
  readonly classes?: string | ((row: T) => string);
  readonly headerStyle?: string;
  readonly headerClasses?: string;
}

/**
 * @interface TableProps<T>
 * @description Tablo bileşeni props'ları
 * @template T - Satır veri tipi
 */
export interface TableProps<T = Record<string, unknown>> extends BaseComponentProps, LoadingProps {
  readonly rows: readonly T[];
  readonly columns: readonly TableColumn<T>[];
  readonly rowKey?: keyof T | ((row: T) => string);
  readonly selection?: 'single' | 'multiple' | 'none';
  readonly selected?: readonly T[];
  readonly pagination?: TablePagination;
  readonly filter?: string;
  readonly separator?: 'horizontal' | 'vertical' | 'cell' | 'none';
  readonly dense?: boolean;
  readonly flat?: boolean;
  readonly bordered?: boolean;
  readonly square?: boolean;
  readonly noDataLabel?: string;
  readonly noResultsLabel?: string;
  readonly loadingLabel?: string;
  readonly rowsPerPageLabel?: string;
}

/**
 * @interface TablePagination
 * @description Tablo sayfalama ayarları
 */
export interface TablePagination {
  readonly page: number;
  readonly rowsPerPage: number;
  readonly rowsNumber?: number;
  readonly sortBy?: string;
  readonly descending?: boolean;
}

/**
 * @interface TableEmits<T>
 * @description Tablo bileşeni emits'leri
 * @template T - Satır veri tipi
 */
export interface TableEmits<T = Record<string, unknown>> {
  'update:selected': [selected: readonly T[]];
  'update:pagination': [pagination: TablePagination];
  'request': [props: { pagination: TablePagination; filter: string }];
  'row-click': [event: Event, row: T, index: number];
  'row-dblclick': [event: Event, row: T, index: number];
  'selection': [details: { rows: readonly T[]; added: boolean; keys: readonly string[] }];
}

/**
 * @description Dialog Types - Dialog bileşen tipleri
 */

/**
 * @interface DialogProps
 * @description Dialog bileşeni props'ları
 */
export interface DialogProps extends BaseComponentProps {
  readonly modelValue: boolean;
  readonly title?: string;
  readonly message?: string;
  readonly persistent?: boolean;
  readonly maximized?: boolean;
  readonly fullWidth?: boolean;
  readonly fullHeight?: boolean;
  readonly position?: 'top' | 'right' | 'bottom' | 'left' | 'standard';
  readonly seamless?: boolean;
  readonly noBackdropDismiss?: boolean;
  readonly noEscDismiss?: boolean;
  readonly noRefocus?: boolean;
  readonly autoClose?: boolean;
}

/**
 * @interface DialogEmits
 * @description Dialog bileşeni emits'leri
 */
export interface DialogEmits {
  'update:modelValue': [value: boolean];
  'show': [];
  'hide': [];
  'before-show': [];
  'before-hide': [];
  'escape-key': [];
  'shake': [];
}

/**
 * @description Composable Types - Composable fonksiyon tipleri
 * Dependency Inversion Principle'a uygun olarak soyut arayüzler
 */

/**
 * @interface UseAsyncStateReturn<T>
 * @description Async state composable dönüş tipi
 * @template T - Async veri tipi
 */
export interface UseAsyncStateReturn<T> {
  readonly state: Ref<T | null>;
  readonly isLoading: Ref<boolean>;
  readonly error: Ref<Error | null>;
  readonly execute: () => Promise<void>;
  readonly refresh: () => Promise<void>;
  readonly reset: () => void;
}

/**
 * @interface UseFormReturn<T>
 * @description Form composable dönüş tipi
 * @template T - Form veri tipi
 */
export interface UseFormReturn<T> {
  readonly formData: Ref<T>;
  readonly errors: Ref<Record<keyof T, string>>;
  readonly isValid: ComputedRef<boolean>;
  readonly isDirty: ComputedRef<boolean>;
  readonly isSubmitting: Ref<boolean>;
  readonly validate: () => Promise<ValidationResult<T>>;
  readonly submit: () => Promise<Result<T, Error>>;
  readonly reset: () => void;
  readonly setFieldValue: <K extends keyof T>(field: K, value: T[K]) => void;
  readonly setFieldError: <K extends keyof T>(field: K, error: string) => void;
  readonly clearErrors: () => void;
}

/**
 * @interface UseTableReturn<T>
 * @description Table composable dönüş tipi
 * @template T - Tablo veri tipi
 */
export interface UseTableReturn<T> {
  readonly rows: Ref<readonly T[]>;
  readonly columns: Ref<readonly TableColumn<T>[]>;
  readonly pagination: Ref<TablePagination>;
  readonly loading: Ref<boolean>;
  readonly selected: Ref<readonly T[]>;
  readonly filter: Ref<string>;
  readonly refresh: () => Promise<void>;
  readonly onRequest: (props: { pagination: TablePagination; filter: string }) => Promise<void>;
  readonly onSelectionChange: (selection: readonly T[]) => void;
  readonly clearSelection: () => void;
}

/**
 * @interface UseDialogReturn
 * @description Dialog composable dönüş tipi
 */
export interface UseDialogReturn {
  readonly isOpen: Ref<boolean>;
  readonly open: () => void;
  readonly close: () => void;
  readonly toggle: () => void;
}

/**
 * @interface UseNotificationReturn
 * @description Notification composable dönüş tipi
 */
export interface UseNotificationReturn {
  readonly notify: (options: NotificationOptions) => void;
  readonly success: (message: string, options?: Partial<NotificationOptions>) => void;
  readonly error: (message: string, options?: Partial<NotificationOptions>) => void;
  readonly warning: (message: string, options?: Partial<NotificationOptions>) => void;
  readonly info: (message: string, options?: Partial<NotificationOptions>) => void;
}

/**
 * @interface NotificationOptions
 * @description Bildirim seçenekleri
 */
export interface NotificationOptions {
  readonly type: 'positive' | 'negative' | 'warning' | 'info';
  readonly message: string;
  readonly caption?: string;
  readonly timeout?: number;
  readonly position?: 'top' | 'top-left' | 'top-right' | 'bottom' | 'bottom-left' | 'bottom-right' | 'left' | 'right' | 'center';
  readonly actions?: NotificationAction[];
  readonly multiLine?: boolean;
  readonly html?: boolean;
  readonly icon?: string;
  readonly avatar?: string;
  readonly color?: string;
  readonly textColor?: string;
  readonly classes?: string;
  readonly attrs?: Record<string, unknown>;
}

/**
 * @interface NotificationAction
 * @description Bildirim aksiyonu
 */
export interface NotificationAction {
  readonly label: string;
  readonly icon?: string;
  readonly color?: string;
  readonly handler?: () => void;
  readonly noDismiss?: boolean;
}

/**
 * @description Router Types - Router tip genişletmeleri
 */

/**
 * @interface UseRouterReturn
 * @description Router composable dönüş tipi
 */
export interface UseRouterReturn {
  readonly router: Router;
  readonly route: ComputedRef<RouteLocationNormalized>;
  readonly push: Router['push'];
  readonly replace: Router['replace'];
  readonly go: Router['go'];
  readonly back: Router['back'];
  readonly forward: Router['forward'];
}

/**
 * @description Theme Types - Tema tip tanımları
 */

/**
 * @interface UseThemeReturn
 * @description Theme composable dönüş tipi
 */
export interface UseThemeReturn {
  readonly isDark: Ref<boolean>;
  readonly theme: ComputedRef<'light' | 'dark'>;
  readonly toggleTheme: () => void;
  readonly setTheme: (theme: 'light' | 'dark') => void;
}

/**
 * @description Language Types - Dil tip tanımları
 */

/**
 * @interface UseI18nReturn
 * @description i18n composable dönüş tipi
 */
export interface UseI18nReturn {
  readonly locale: WritableComputedRef<string>;
  readonly availableLocales: ComputedRef<readonly string[]>;
  readonly t: (key: string, params?: Record<string, unknown>) => string;
  readonly setLocale: (locale: string) => void;
}
