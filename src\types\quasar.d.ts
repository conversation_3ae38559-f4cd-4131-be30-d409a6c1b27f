/**
 * @fileoverview Quasar Framework TypeScript tip tanımları
 * @description Bu dosya Quasar Framework bileşenleri ve composable'lar<PERSON> için
 * özel tip tanımlarını ve genişletmeleri içerir. SOLID prensiplerinden
 * Single Responsibility Principle'a uygun olarak sadece Quasar ile ilgili tipler tanımlanır.
 */

import type { QTableProps, QTableColumn } from 'quasar';
import type { BaseComponentProps, ColorProps, SizeProps } from './vue';

/**
 * @description Quasar Component Extensions - Quasar bileşen genişletmeleri
 * Open/Closed Principle'a uygun olarak mevcut Quasar tiplerini genişletir
 */

/**
 * @interface ExtendedQTableColumn<T>
 * @description Genişletilmiş QTable sütun tanımı
 * @template T - Satır veri tipi
 */
export interface ExtendedQTableColumn<T = Record<string, unknown>> extends QTableColumn {
  readonly field: keyof T | ((row: T) => unknown);
  readonly format?: (value: unknown, row: T) => string;
  readonly style?: string | ((row: T) => string);
  readonly classes?: string | ((row: T) => string);
  readonly sortable?: boolean;
  readonly filterable?: boolean;
  readonly resizable?: boolean;
  readonly hideable?: boolean;
  readonly exportable?: boolean;
}

/**
 * @interface ExtendedQTableProps<T>
 * @description Genişletilmiş QTable props
 * @template T - Satır veri tipi
 */
export interface ExtendedQTableProps<T = Record<string, unknown>> extends Omit<QTableProps, 'columns'> {
  readonly columns: readonly ExtendedQTableColumn<T>[];
  readonly exportable?: boolean;
  readonly exportFormats?: readonly ('csv' | 'excel' | 'pdf')[];
  readonly printable?: boolean;
  readonly columnPicker?: boolean;
  readonly globalFilter?: boolean;
  readonly advancedFilter?: boolean;
}

/**
 * @description Quasar Layout Types - Layout bileşen tipleri
 */

/**
 * @interface QLayoutProps
 * @description QLayout bileşeni için özel props
 */
export interface QLayoutProps extends BaseComponentProps {
  readonly view?: string;
  readonly container?: boolean;
  readonly reveal?: boolean;
  readonly bordered?: boolean;
  readonly elevated?: boolean;
  readonly padding?: boolean;
}

/**
 * @interface QHeaderProps
 * @description QHeader bileşeni için özel props
 */
export interface QHeaderProps extends BaseComponentProps, ColorProps {
  readonly modelValue?: boolean;
  readonly reveal?: boolean;
  readonly revealOffset?: number;
  readonly bordered?: boolean;
  readonly elevated?: boolean;
  readonly heightHint?: number;
}

/**
 * @interface QDrawerProps
 * @description QDrawer bileşeni için özel props
 */
export interface QDrawerProps extends BaseComponentProps, ColorProps {
  readonly modelValue?: boolean;
  readonly side?: 'left' | 'right';
  readonly overlay?: boolean;
  readonly persistent?: boolean;
  readonly noSwipeOpen?: boolean;
  readonly noSwipeClose?: boolean;
  readonly noSwipeBackdrop?: boolean;
  readonly width?: number;
  readonly mini?: boolean;
  readonly miniToOverlay?: boolean;
  readonly miniWidth?: number;
  readonly breakpoint?: number;
  readonly behavior?: 'default' | 'desktop' | 'mobile';
  readonly bordered?: boolean;
  readonly elevated?: boolean;
  readonly dark?: boolean;
}

/**
 * @description Quasar Form Types - Form bileşen tipleri
 */

/**
 * @interface QInputProps
 * @description QInput bileşeni için genişletilmiş props
 */
export interface QInputProps extends BaseComponentProps, ColorProps, SizeProps {
  readonly modelValue?: string | number;
  readonly type?: 'text' | 'password' | 'email' | 'search' | 'tel' | 'file' | 'number' | 'url' | 'time' | 'date';
  readonly label?: string;
  readonly placeholder?: string;
  readonly hint?: string;
  readonly prefix?: string;
  readonly suffix?: string;
  readonly loading?: boolean;
  readonly clearable?: boolean;
  readonly clearIcon?: string;
  readonly filled?: boolean;
  readonly outlined?: boolean;
  readonly borderless?: boolean;
  readonly standout?: boolean | string;
  readonly hideBottomSpace?: boolean;
  readonly rounded?: boolean;
  readonly square?: boolean;
  readonly dense?: boolean;
  readonly itemAligned?: boolean;
  readonly disable?: boolean;
  readonly readonly?: boolean;
  readonly autofocus?: boolean;
  readonly for?: string;
  readonly maxlength?: number | string;
  readonly error?: boolean;
  readonly errorMessage?: string;
  readonly noErrorIcon?: boolean;
  readonly rules?: readonly ((val: unknown) => boolean | string)[];
  readonly reactiveRules?: boolean;
  readonly lazyRules?: boolean | 'ondemand';
}

/**
 * @interface QSelectProps<T>
 * @description QSelect bileşeni için genişletilmiş props
 * @template T - Seçenek veri tipi
 */
export interface QSelectProps<T = unknown> extends Omit<QInputProps, 'modelValue' | 'type'> {
  readonly modelValue?: T | readonly T[];
  readonly options: readonly T[];
  readonly optionValue?: keyof T | ((option: T) => unknown);
  readonly optionLabel?: keyof T | ((option: T) => string);
  readonly optionDisable?: keyof T | ((option: T) => boolean);
  readonly multiple?: boolean;
  readonly displayValue?: string;
  readonly displayValueHtml?: boolean;
  readonly dropdownIcon?: string;
  readonly maxValues?: number;
  readonly optionsDense?: boolean;
  readonly optionsDark?: boolean;
  readonly optionsSelectedClass?: string;
  readonly optionsCover?: boolean;
  readonly menuShrink?: boolean;
  readonly menuAnchor?: string;
  readonly menuSelf?: string;
  readonly menuOffset?: readonly [number, number];
  readonly popupContentClass?: string;
  readonly popupContentStyle?: string | Record<string, string>;
  readonly useInput?: boolean;
  readonly useChips?: boolean;
  readonly fillInput?: boolean;
  readonly newValueMode?: 'add' | 'add-unique' | 'toggle';
  readonly mapOptions?: boolean;
  readonly emitValue?: boolean;
  readonly inputDebounce?: number | string;
  readonly inputClass?: string;
  readonly inputStyle?: string | Record<string, string>;
  readonly tabindex?: number | string;
  readonly autocomplete?: string;
  readonly transitionShow?: string;
  readonly transitionHide?: string;
  readonly behavior?: 'default' | 'menu' | 'dialog';
}

/**
 * @interface QBtnProps
 * @description QBtn bileşeni için genişletilmiş props
 */
export interface QBtnProps extends BaseComponentProps, ColorProps, SizeProps {
  readonly type?: 'button' | 'submit' | 'reset';
  readonly to?: string | Record<string, unknown>;
  readonly replace?: boolean;
  readonly href?: string;
  readonly target?: string;
  readonly label?: string | number;
  readonly icon?: string;
  readonly iconRight?: string;
  readonly outline?: boolean;
  readonly flat?: boolean;
  readonly unelevated?: boolean;
  readonly rounded?: boolean;
  readonly push?: boolean;
  readonly glossy?: boolean;
  readonly fab?: boolean;
  readonly fabMini?: boolean;
  readonly padding?: string;
  readonly dense?: boolean;
  readonly ripple?: boolean | Record<string, unknown>;
  readonly round?: boolean;
  readonly square?: boolean;
  readonly disable?: boolean;
  readonly loading?: boolean;
  readonly percentage?: number;
  readonly darkPercentage?: boolean;
  readonly align?: 'left' | 'right' | 'center' | 'around' | 'between' | 'evenly';
  readonly stack?: boolean;
  readonly stretch?: boolean;
  readonly noWrap?: boolean;
  readonly noCaps?: boolean;
  readonly noRipple?: boolean;
  readonly tabindex?: number | string;
}

/**
 * @description Quasar Dialog Types - Dialog bileşen tipleri
 */

/**
 * @interface QDialogProps
 * @description QDialog bileşeni için genişletilmiş props
 */
export interface QDialogProps extends BaseComponentProps {
  readonly modelValue?: boolean;
  readonly persistent?: boolean;
  readonly noEscDismiss?: boolean;
  readonly noBackdropDismiss?: boolean;
  readonly noRouteDismiss?: boolean;
  readonly autoClose?: boolean;
  readonly seamless?: boolean;
  readonly maximized?: boolean;
  readonly fullWidth?: boolean;
  readonly fullHeight?: boolean;
  readonly square?: boolean;
  readonly position?: 'top' | 'right' | 'bottom' | 'left' | 'standard';
  readonly transitionShow?: string;
  readonly transitionHide?: string;
  readonly transitionDuration?: string | number;
  readonly noRefocus?: boolean;
  readonly noFocus?: boolean;
  readonly allowFocusOutside?: boolean;
}

/**
 * @description Quasar Notification Types - Bildirim tipleri
 */

/**
 * @interface QNotifyOptions
 * @description Quasar bildirim seçenekleri
 */
export interface QNotifyOptions {
  readonly type?: 'positive' | 'negative' | 'warning' | 'info' | 'ongoing';
  readonly color?: string;
  readonly textColor?: string;
  readonly message?: string;
  readonly caption?: string;
  readonly html?: boolean;
  readonly icon?: string;
  readonly iconColor?: string;
  readonly iconSize?: string;
  readonly avatar?: string;
  readonly spinner?: boolean;
  readonly spinnerColor?: string;
  readonly spinnerSize?: string;
  readonly position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top' | 'bottom' | 'left' | 'right' | 'center';
  readonly group?: boolean | string | number;
  readonly badgeColor?: string;
  readonly badgeTextColor?: string;
  readonly badgePosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  readonly badgeStyle?: string | readonly string[] | Record<string, unknown>;
  readonly badgeClass?: string | readonly string[] | Record<string, unknown>;
  readonly progress?: boolean;
  readonly progressClass?: string | readonly string[] | Record<string, unknown>;
  readonly classes?: string | readonly string[] | Record<string, unknown>;
  readonly attrs?: Record<string, unknown>;
  readonly timeout?: number;
  readonly actions?: readonly QNotifyAction[];
  readonly onDismiss?: () => void;
  readonly closeBtn?: boolean | string;
  readonly multiLine?: boolean;
  readonly ignoreDefaults?: boolean;
}

/**
 * @interface QNotifyAction
 * @description Quasar bildirim aksiyonu
 */
export interface QNotifyAction {
  readonly icon?: string;
  readonly label?: string;
  readonly color?: string;
  readonly size?: string;
  readonly handler?: () => void;
  readonly noDismiss?: boolean;
  readonly attrs?: Record<string, unknown>;
}

/**
 * @description Quasar Loading Types - Yükleme tipleri
 */

/**
 * @interface QLoadingOptions
 * @description Quasar loading seçenekleri
 */
export interface QLoadingOptions {
  readonly spinner?: unknown;
  readonly spinnerColor?: string;
  readonly spinnerSize?: string;
  readonly backgroundColor?: string;
  readonly message?: string;
  readonly messageColor?: string;
  readonly customClass?: string;
  readonly delay?: number;
  readonly group?: string;
  readonly boxClass?: string;
  readonly sanitize?: boolean;
  readonly html?: boolean;
}

/**
 * @description Quasar Theme Types - Tema tipleri
 */

/**
 * @interface QuasarTheme
 * @description Quasar tema konfigürasyonu
 */
export interface QuasarTheme {
  readonly dark?: boolean;
  readonly primary?: string;
  readonly secondary?: string;
  readonly accent?: string;
  readonly positive?: string;
  readonly negative?: string;
  readonly info?: string;
  readonly warning?: string;
}

/**
 * @description Quasar Platform Types - Platform tipleri
 */

/**
 * @interface QuasarPlatform
 * @description Quasar platform bilgileri
 */
export interface QuasarPlatform {
  readonly is: {
    readonly android: boolean;
    readonly blackberry: boolean;
    readonly cros: boolean;
    readonly ios: boolean;
    readonly ipad: boolean;
    readonly iphone: boolean;
    readonly kindle: boolean;
    readonly linux: boolean;
    readonly mac: boolean;
    readonly playbook: boolean;
    readonly silk: boolean;
    readonly chrome: boolean;
    readonly opera: boolean;
    readonly safari: boolean;
    readonly win: boolean;
    readonly winphone: boolean;
    readonly bada: boolean;
    readonly cordova: boolean;
    readonly electron: boolean;
    readonly nativescript: boolean;
    readonly desktop: boolean;
    readonly mobile: boolean;
    readonly capacitor: boolean;
    readonly pwa: boolean;
    readonly ssr: boolean;
    readonly spa: boolean;
    readonly webview: boolean;
  };
  readonly has: {
    readonly touch: boolean;
    readonly webStorage: boolean;
  };
  readonly within: {
    readonly iframe: boolean;
  };
}
