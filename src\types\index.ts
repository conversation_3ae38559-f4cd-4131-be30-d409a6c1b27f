/**
 * @fileoverview TypeScript tip tanımları merkezi export dosyası
 * @description Bu dosya tüm tip tanımlarını merkezi bir noktadan export eder.
 * SOLID prensiplerinden Single Responsibility Principle'a uygun olarak
 * sadece tip export işlemini gerçekleştirir.
 * 
 * Barrel export pattern kullanılarak kod organizasyonu sağlanır.
 * Bu yaklaşım Open/Closed Principle'a uygun olarak yeni tipler
 * eklendiğinde mevcut import'ları bozmaz.
 */

/**
 * @description Global Types Export
 * Temel utility tipler ve ortak veri yapıları
 */
export type {
  // Utility Types
  DeepPartial,
  DeepRequired,
  Optional,
  RequiredKeys,
  OptionalKeys,
  
  // Brand Types
  Brand,
  ID,
  Email,
  URL,
  Timestamp,
  
  // Base Interfaces
  BaseEntity,
  Identifiable,
  Timestamped,
  Nameable,
  Describable,
  
  // Result Types
  Result,
  Success,
  Failure,
  
  // Pagination Types
  PaginationParams,
  PaginatedResult,
  
  // Sort Types
  SortDirection,
  SortParams,
  
  // Filter Types
  FilterOperator,
  FilterCondition,
  
  // Validation Types
  ValidationError,
  ValidationResult,
} from './global';

/**
 * @description API Types Export
 * HTTP işlemleri ve API request/response tipleri
 */
export type {
  // HTTP Types
  HttpMethod,
  HttpStatusCode,
  HttpHeaders,
  
  // Request Types
  BaseRequest,
  GetRequest,
  PostRequest,
  PutRequest,
  PatchRequest,
  DeleteRequest,
  
  // Response Types
  ApiResponse,
  ApiSuccessResponse,
  ApiErrorResponse,
  ApiError,
  
  // Query Types
  QueryParams,
  ListQueryParams,
  SearchQueryParams,
  
  // CRUD Operation Types
  CreateOperation,
  ReadOperation,
  UpdateOperation,
  DeleteOperation,
  
  // Batch Operation Types
  BatchCreateOperation,
  BatchUpdateOperation,
  BatchDeleteOperation,
  
  // API Result Types
  ApiResult,
  ListResult,
  CreateResult,
  UpdateResult,
  DeleteResult,
  
  // Authentication Types
  AuthTokens,
  LoginRequest,
  RefreshTokenRequest,
} from './api';

/**
 * @description Vue Types Export
 * Vue 3 Composition API ve component tipleri
 */
export type {
  // Component Props
  BaseComponentProps,
  LoadingProps,
  DisabledProps,
  SizeProps,
  ColorProps,
  
  // Form Types
  FormFieldProps,
  FormFieldEmits,
  ValidationProps,
  ValidationRule,
  
  // Table Types
  TableColumn,
  TableProps,
  TablePagination,
  TableEmits,
  
  // Dialog Types
  DialogProps,
  DialogEmits,
  
  // Composable Return Types
  UseAsyncStateReturn,
  UseFormReturn,
  UseTableReturn,
  UseDialogReturn,
  UseNotificationReturn,
  UseRouterReturn,
  UseThemeReturn,
  UseI18nReturn,
  
  // Notification Types
  NotificationOptions,
  NotificationAction,
} from './vue';

/**
 * @description Quasar Types Export
 * Quasar Framework özel tip tanımları
 */
export type {
  // Extended Quasar Components
  ExtendedQTableColumn,
  ExtendedQTableProps,
  
  // Layout Components
  QLayoutProps,
  QHeaderProps,
  QDrawerProps,
  
  // Form Components
  QInputProps,
  QSelectProps,
  QBtnProps,
  
  // Dialog Components
  QDialogProps,
  
  // Notification Types
  QNotifyOptions,
  QNotifyAction,
  
  // Loading Types
  QLoadingOptions,
  
  // Theme Types
  QuasarTheme,
  
  // Platform Types
  QuasarPlatform,
} from './quasar';

/**
 * @description Pinia Store Types Export
 * Pinia store state, actions ve getters tipleri
 */
export type {
  // Base Store Types
  BaseStoreState,
  BaseStoreActions,
  BaseStoreGetters,
  
  // Theme Store Types
  ThemeMode,
  ThemeStoreState,
  ThemeStoreActions,
  ThemeStoreGetters,
  ThemeStore,
  
  // Language Store Types
  SupportedLocale,
  LanguageStoreState,
  LanguageStoreActions,
  LanguageStoreGetters,
  LanguageStore,
  
  // Layout Store Types
  LayoutMode,
  LayoutStoreState,
  LayoutStoreActions,
  LayoutStoreGetters,
  LayoutStore,
  
  // User Store Types
  User,
  UserStoreState,
  LoginCredentials,
  UserStoreActions,
  UserStoreGetters,
  UserStore,
  
  // Store Factory Types
  StoreFactory,
  StoreRegistry,
  
  // Store Persistence Types
  StorePersistenceOptions,
  PersistentStore,
} from './pinia';

/**
 * @description Type Guards - Tip koruma fonksiyonları
 * Runtime'da tip güvenliği sağlamak için kullanılır
 */

/**
 * @function isID
 * @description ID tipini kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns ID tipi olup olmadığı
 */
export function isID(value: unknown): value is ID {
  return typeof value === 'string' && value.length > 0;
}

/**
 * @function isEmail
 * @description Email tipini kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns Email tipi olup olmadığı
 */
export function isEmail(value: unknown): value is Email {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return typeof value === 'string' && emailRegex.test(value);
}

/**
 * @function isURL
 * @description URL tipini kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns URL tipi olup olmadığı
 */
export function isURL(value: unknown): value is URL {
  try {
    new globalThis.URL(value as string);
    return true;
  } catch {
    return false;
  }
}

/**
 * @function isTimestamp
 * @description Timestamp tipini kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns Timestamp tipi olup olmadığı
 */
export function isTimestamp(value: unknown): value is Timestamp {
  return typeof value === 'number' && value > 0 && Number.isInteger(value);
}

/**
 * @function isResult
 * @description Result tipini kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns Result tipi olup olmadığı
 */
export function isResult<T, E>(value: unknown): value is Result<T, E> {
  return (
    typeof value === 'object' &&
    value !== null &&
    'success' in value &&
    typeof (value as { success: unknown }).success === 'boolean'
  );
}

/**
 * @function isSuccess
 * @description Success tipini kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns Success tipi olup olmadığı
 */
export function isSuccess<T>(value: unknown): value is Success<T> {
  return (
    isResult(value) &&
    value.success === true &&
    'data' in value
  );
}

/**
 * @function isFailure
 * @description Failure tipini kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns Failure tipi olup olmadığı
 */
export function isFailure<E>(value: unknown): value is Failure<E> {
  return (
    isResult(value) &&
    value.success === false &&
    'error' in value
  );
}

/**
 * @function isApiResponse
 * @description ApiResponse tipini kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns ApiResponse tipi olup olmadığı
 */
export function isApiResponse<T>(value: unknown): value is ApiResponse<T> {
  return (
    typeof value === 'object' &&
    value !== null &&
    'data' in value &&
    'status' in value &&
    'statusText' in value &&
    'headers' in value
  );
}

/**
 * @function isValidationError
 * @description ValidationError tipini kontrol eder
 * @param value - Kontrol edilecek değer
 * @returns ValidationError tipi olup olmadığı
 */
export function isValidationError(value: unknown): value is ValidationError {
  return (
    typeof value === 'object' &&
    value !== null &&
    'field' in value &&
    'message' in value &&
    'code' in value &&
    typeof (value as ValidationError).field === 'string' &&
    typeof (value as ValidationError).message === 'string' &&
    typeof (value as ValidationError).code === 'string'
  );
}

/**
 * @description Type Assertion Helpers - Tip onaylama yardımcıları
 * Güvenli tip dönüşümü için kullanılır
 */

/**
 * @function assertID
 * @description Değerin ID tipi olduğunu onaylar
 * @param value - Onaylanacak değer
 * @throws Error - Değer ID tipi değilse
 */
export function assertID(value: unknown): asserts value is ID {
  if (!isID(value)) {
    throw new Error(`Expected ID, got ${typeof value}`);
  }
}

/**
 * @function assertEmail
 * @description Değerin Email tipi olduğunu onaylar
 * @param value - Onaylanacak değer
 * @throws Error - Değer Email tipi değilse
 */
export function assertEmail(value: unknown): asserts value is Email {
  if (!isEmail(value)) {
    throw new Error(`Expected Email, got ${typeof value}`);
  }
}

/**
 * @function assertResult
 * @description Değerin Result tipi olduğunu onaylar
 * @param value - Onaylanacak değer
 * @throws Error - Değer Result tipi değilse
 */
export function assertResult<T, E>(value: unknown): asserts value is Result<T, E> {
  if (!isResult<T, E>(value)) {
    throw new Error(`Expected Result, got ${typeof value}`);
  }
}
